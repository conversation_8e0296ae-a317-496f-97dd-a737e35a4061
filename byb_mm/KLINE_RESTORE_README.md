# BYBUSDT K线数据完整处理系统

## 概述

`kline_restore.py` 是一个完整的K线数据处理系统，集成了所有K线修复代码、多时间周期重采样、插针优化和数据可视化功能。

## 主要功能

### 1. 数据处理和K线生成
- 从Excel交易数据生成1分钟K线
- OHLC平滑优化处理，限制单根K线波动不超过1%
- 使用high/low-1公式计算波动率
- **影线修复**：缩短长影线，确保影线长度不超过实体长度
- **震荡平滑处理**：检测并平滑几分钟内反复上下震荡的K线，消除震荡

### 2. 多时间周期重采样
支持以下时间周期的K线数据生成：
- **5分钟** (5T)
- **15分钟** (15T) 
- **30分钟** (30T)
- **1小时** (1H)
- **1天** (1D)

### 3. 插针检测和优化
- 自动检测价格异常波动（插针）
- 基于移动平均和标准差的插针识别
- 智能优化插针，保持价格合理性

### 4. 影线修复和震荡平滑
- **影线修复**：自动缩短过长的上下影线
- **震荡检测**：识别几分钟内反复上下震荡的价格模式
- **震荡平滑**：使用线性回归对震荡区间进行平滑处理
- **趋势保持**：在平滑过程中保持整体价格趋势

### 5. 数据可视化
- **1分钟K线图**：包含价格和交易量
- **多时间周期对比图**：展示不同时间周期的价格走势
- **高质量图表**：300 DPI分辨率，适合报告使用

### 6. 综合数据分析
- 详细的统计分析报告
- 数据质量评估
- 波动率统计
- 涨跌分布分析

## 使用方法

### 前置条件
确保以下文件存在于 `byb_mm` 目录下：
```
用户实时交易单2025-07-03-2025-07-03.xls
```

### 运行脚本
```bash
cd byb_mm
python kline_restore.py
```

### 输出文件

#### K线数据文件
- `bybusdt_1min_kline_2025-07-03.csv` - 1分钟K线数据
- `bybusdt_5T_kline_2025-07-03.csv` - 5分钟K线数据
- `bybusdt_15T_kline_2025-07-03.csv` - 15分钟K线数据
- `bybusdt_30T_kline_2025-07-03.csv` - 30分钟K线数据
- `bybusdt_1H_kline_2025-07-03.csv` - 1小时K线数据
- `bybusdt_1D_kline_2025-07-03.csv` - 1天K线数据

#### 图表文件
- `bybusdt_1分钟_kline_chart_*.png` - 1分钟K线图
- `bybusdt_multi_timeframe_comparison_*.png` - 多时间周期对比图

#### 分析报告
- `bybusdt_kline_analysis_report_*.txt` - 综合数据分析报告

## 数据格式

所有K线数据文件采用统一格式：
```csv
timestamp_sec,amount,vol,open,close,high,low
```

字段说明：
- `timestamp_sec`: 时间戳（秒）
- `amount`: 交易额（USDT）
- `vol`: 交易量（BYB）
- `open`: 开盘价
- `close`: 收盘价
- `high`: 最高价
- `low`: 最低价

## 优化特性

### OHLC平滑优化
- 指数移动平均平滑收盘价
- 限制开盘价跳空幅度
- 缩短长影线长度
- 确保波动率控制在合理范围内

### 影线修复
- 自动检测过长的上下影线
- 限制影线长度不超过实体长度
- 保持K线形态的合理性

### 震荡平滑处理
- 检测5分钟窗口内的价格震荡模式
- 识别频繁的价格方向变化
- 使用线性回归平滑震荡区间
- 消除反复上下震荡，保持趋势连续性

### 插针优化
- 基于2.5倍标准差的插针检测
- 智能调整异常高低价
- 保持价格的连续性和合理性

### 数据质量控制
- 自动检测和处理异常数据
- 数据质量评分系统
- 详细的优化效果统计

## 技术参数

### 平滑优化参数
- 最大波动率限制：1% (high/low-1)
- 平滑因子：0.6
- 影线长度限制：不超过实体长度

### 影线修复参数
- 影线/实体比例限制：1.0倍（影线≤实体）
- 最小实体比例：0.1%
- 自动检测和缩短过长影线

### 震荡平滑参数
- 检测窗口：5分钟
- 震荡阈值：0.5%价格变化
- 平滑强度：0.8（80%趋势+20%原始）
- 最小震荡范围：1%

### 插针检测参数
- 检测阈值：2.5倍标准差
- 移动平均窗口：20期
- 优化幅度：最大2%调整

## 性能特点

- **高效处理**：优化的数据处理算法
- **内存友好**：分步处理大数据集
- **错误处理**：完善的异常处理机制
- **进度显示**：实时处理进度反馈

## 注意事项

1. **数据源要求**：确保Excel文件包含完整的交易数据
2. **时间格式**：支持标准的时间戳格式
3. **数据质量**：建议使用高质量的原始交易数据
4. **存储空间**：确保有足够的磁盘空间存储输出文件

## 故障排除

### 常见问题

**问题1：找不到Excel文件**
```
错误: 未找到Excel文件 '用户实时交易单2025-07-03-2025-07-03.xls'
```
解决方案：确保Excel文件在当前目录下，文件名完全匹配。

**问题2：数据质量评分过低**
```
数据质量评分: 15.0/100
⚠ 数据质量一般，建议进一步优化
```
解决方案：这是正常现象，表示原始数据波动较大，系统已进行优化处理。

**问题3：内存不足**
解决方案：处理大数据集时，确保系统有足够的可用内存。

## 更新日志

### v1.1.0 (2025-07-03)
- 集成所有K线修复功能
- 添加多时间周期重采样
- 实现插针检测和优化
- **新增影线修复功能**
- **新增震荡检测和平滑处理**
- 集成数据可视化功能
- 添加综合分析报告

## 技术支持

如有问题或建议，请联系开发团队。

---

**开发者**: Augment Agent
**最后更新**: 2025-07-03
**版本**: 1.1.0
