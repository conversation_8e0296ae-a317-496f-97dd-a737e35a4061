import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def process_bybusdt_trades_to_kline():
    """
    将bybusdt交易记录整理成1分钟K线数据
    数据格式：时间戳(秒) amount交易额 vol交易量 open开盘价 close收盘价 high最高价 low最低价
    """
    print("正在读取Excel文件...")

    # 读取Excel文件
    df = pd.read_excel('用户实时交易单2025-07-03-2025-07-03.xls')
    print(f"总交易记录数: {len(df)}")

    # 筛选bybusdt交易对
    df = df[df['币对'] == 'bybusdt']
    print(f"bybusdt交易记录数: {len(df)}")

    if len(df) == 0:
        print("未找到bybusdt交易记录")
        return

    # 数据预处理
    print("正在处理数据...")

    # 转换时间格式
    df['timestamp'] = pd.to_datetime(df['成交時間'])

    # 转换数值类型 - 处理可能的字符串格式
    df['成交数量'] = pd.to_numeric(df['成交数量'].astype(str).str.replace(' BYB', ''), errors='coerce')
    df['成交价格'] = pd.to_numeric(df['成交价格'].astype(str).str.replace(' USDT', ''), errors='coerce')

    # 计算交易额 (成交数量 * 成交价格)
    df['交易额'] = df['成交数量'] * df['成交价格']

    # 删除无效数据
    df = df.dropna(subset=['成交数量', '成交价格', '交易额'])
    print(f"有效交易记录数: {len(df)}")

    # 按时间排序
    df = df.sort_values('timestamp')

    # 设置时间为索引，并重采样为1分钟
    df.set_index('timestamp', inplace=True)

    print("正在生成1分钟K线数据...")

    # 生成1分钟K线数据
    kline_data = df.resample('1T').agg({
        '成交价格': ['first', 'max', 'min', 'last'],  # 开盘价、最高价、最低价、收盘价
        '交易额': 'sum',                              # 交易额总和
        '成交数量': 'sum'                             # 交易量总和
    }).dropna()

    # 重命名列
    kline_data.columns = ['open', 'high', 'low', 'close', 'amount', 'vol']

    # 重置索引，获取时间戳
    kline_data.reset_index(inplace=True)

    # 转换时间戳为秒
    kline_data['timestamp_sec'] = kline_data['timestamp'].astype('int64') // 10**9

    # 重新排列列的顺序
    result = kline_data[['timestamp_sec', 'amount', 'vol', 'open', 'close', 'high', 'low']].copy()

    # 应用OHLC平滑优化
    print("正在应用OHLC平滑优化...")
    result = apply_ohlc_smoothing(result, max_volatility=0.01, smoothing_factor=0.6)  # 限制单根K线波动(high/low-1)不超过1%

    # 格式化数值显示
    result['amount'] = result['amount'].round(6)
    result['vol'] = result['vol'].round(6)
    result['open'] = result['open'].round(6)
    result['close'] = result['close'].round(6)
    result['high'] = result['high'].round(6)
    result['low'] = result['low'].round(6)

    print(f"生成K线数据条数: {len(result)}")
    print("\n前10条K线数据:")
    print(result.head(10).to_string(index=False))

    # 保存到CSV文件
    output_file = 'bybusdt_1min_kline_2025-07-03.csv'
    result.to_csv(output_file, index=False)
    print(f"\nK线数据已保存到: {output_file}")

    # 显示统计信息
    print(f"\n统计信息:")
    print(f"时间范围: {kline_data['timestamp'].min()} 到 {kline_data['timestamp'].max()}")
    print(f"总交易额: {result['amount'].sum():.2f} USDT")
    print(f"总交易量: {result['vol'].sum():.2f} BYB")
    print(f"价格范围: {result['low'].min():.6f} - {result['high'].max():.6f} USDT")

    return result

def apply_ohlc_smoothing(df, max_volatility=0.01, smoothing_factor=0.6):
    """
    对OHLC四个价格同时进行平滑优化，确保 high/low - 1 不超过1%
    Args:
        df: K线数据DataFrame
        max_volatility: 单根K线最大允许波动率，使用high/low-1公式 (默认1%)
        smoothing_factor: 平滑因子 (0-1之间，越大平滑程度越高)
    Returns:
        优化后的DataFrame
    """
    df_smoothed = df.copy()
    optimization_count = 0

    print(f"开始OHLC平滑优化，限制单根K线波动不超过{max_volatility*100:.1f}%...")

    # 第一步：对收盘价进行指数移动平均平滑
    alpha = smoothing_factor
    smoothed_close = [df_smoothed['close'].iloc[0]]

    for i in range(1, len(df_smoothed)):
        # 指数移动平均
        new_close = alpha * df_smoothed['close'].iloc[i] + (1 - alpha) * smoothed_close[-1]
        smoothed_close.append(new_close)

    df_smoothed['close'] = smoothed_close

    # 第二步：基于平滑后的收盘价调整开盘价
    for i in range(1, len(df_smoothed)):
        prev_close = df_smoothed['close'].iloc[i-1]
        current_open = df_smoothed['open'].iloc[i]

        # 限制开盘价跳空幅度
        max_gap = prev_close * max_volatility
        if abs(current_open - prev_close) > max_gap:
            if current_open > prev_close:
                df_smoothed.iloc[i, df_smoothed.columns.get_loc('open')] = prev_close + max_gap
            else:
                df_smoothed.iloc[i, df_smoothed.columns.get_loc('open')] = prev_close - max_gap
            optimization_count += 1

    # 第三步：对开盘价也进行轻度平滑
    smoothed_open = [df_smoothed['open'].iloc[0]]
    for i in range(1, len(df_smoothed)):
        # 使用较小的平滑因子保持开盘价的相对独立性
        new_open = (smoothing_factor * 0.5) * df_smoothed['open'].iloc[i] + (1 - smoothing_factor * 0.5) * smoothed_open[-1]
        smoothed_open.append(new_open)

    df_smoothed['open'] = smoothed_open

    # 第四步：缩短长影线并优化每根K线的高低价，确保波动不超过限制
    for i in range(len(df_smoothed)):
        open_price = df_smoothed['open'].iloc[i]
        close_price = df_smoothed['close'].iloc[i]
        high_price = df_smoothed['high'].iloc[i]
        low_price = df_smoothed['low'].iloc[i]

        # 缩短长影线：限制影线长度不超过实体长度
        body_size = abs(close_price - open_price)
        body_center = (open_price + close_price) / 2

        # 如果实体很小，使用最小实体大小来计算影线限制
        min_body_size = body_center * 0.001  # 最小实体为价格的0.1%
        effective_body_size = max(body_size, min_body_size)

        # 影线长度限制：不超过实体大小的1倍（即影线≤实体）
        max_shadow_length = effective_body_size * 1.0

        # 缩短上影线
        max_allowed_high = max(open_price, close_price) + max_shadow_length
        if high_price > max_allowed_high:
            high_price = max_allowed_high
            df_smoothed.iloc[i, df_smoothed.columns.get_loc('high')] = high_price

        # 缩短下影线
        min_allowed_low = min(open_price, close_price) - max_shadow_length
        if low_price < min_allowed_low:
            low_price = min_allowed_low
            df_smoothed.iloc[i, df_smoothed.columns.get_loc('low')] = low_price

        # 计算当前K线的波动率 (high/low - 1)
        if low_price > 0:
            current_volatility = high_price / low_price - 1
        else:
            current_volatility = 0

        if current_volatility > max_volatility:  # 如果波动超过限制
            # 基于high/low-1公式重新计算
            # 目标：high/low - 1 = max_volatility
            # 即：high = low * (1 + max_volatility)

            # 确保高低价包含开盘价和收盘价
            min_high = max(open_price, close_price)
            max_low = min(open_price, close_price)

            # 方法1：以低价为基准调整高价
            target_high_from_low = max_low * (1 + max_volatility)
            if target_high_from_low >= min_high:
                new_low = max_low
                new_high = target_high_from_low
            else:
                # 方法2：以高价为基准调整低价
                new_high = min_high
                new_low = new_high / (1 + max_volatility)

                # 确保低价不高于开盘收盘价的最小值
                if new_low > max_low:
                    # 如果调整后的低价仍然太高，需要平衡调整
                    # 在开盘收盘价范围内找到合适的高低价
                    oc_mid = (open_price + close_price) / 2
                    oc_range = abs(open_price - close_price)

                    # 以开盘收盘价中点为基准，向两边扩展
                    half_target_range = oc_mid * max_volatility / 2
                    new_high = oc_mid + half_target_range
                    new_low = oc_mid - half_target_range

                    # 确保包含开盘收盘价
                    new_high = max(new_high, min_high)
                    new_low = min(new_low, max_low)

            df_smoothed.iloc[i, df_smoothed.columns.get_loc('high')] = new_high
            df_smoothed.iloc[i, df_smoothed.columns.get_loc('low')] = new_low
            optimization_count += 1

    # 第五步：最终一致性检查和微调
    for i in range(len(df_smoothed)):
        open_price = df_smoothed['open'].iloc[i]
        close_price = df_smoothed['close'].iloc[i]
        high_price = df_smoothed['high'].iloc[i]
        low_price = df_smoothed['low'].iloc[i]

        # 确保OHLC逻辑正确
        corrected_high = max(high_price, open_price, close_price)
        corrected_low = min(low_price, open_price, close_price)

        # 最终波动检查 (使用high/low-1公式)
        if corrected_low > 0:
            final_volatility = corrected_high / corrected_low - 1

            if final_volatility > max_volatility:  # 如果仍然超过限制
                # 进行最终调整 - 严格控制在限制范围内
                # 目标：high/low - 1 = max_volatility

                # 确保包含开盘价和收盘价
                min_high = max(open_price, close_price)
                max_low = min(open_price, close_price)

                # 以低价为基准调整
                target_high = max_low * (1 + max_volatility)
                if target_high >= min_high:
                    corrected_low = max_low
                    corrected_high = target_high
                else:
                    # 以高价为基准调整
                    corrected_high = min_high
                    corrected_low = corrected_high / (1 + max_volatility)

                    # 确保低价合理
                    if corrected_low > max_low:
                        # 强制调整：在开盘收盘价范围内
                        corrected_high = min_high
                        corrected_low = max_low

        df_smoothed.iloc[i, df_smoothed.columns.get_loc('high')] = corrected_high
        df_smoothed.iloc[i, df_smoothed.columns.get_loc('low')] = corrected_low

    # 统计优化效果
    original_volatilities = []
    smoothed_volatilities = []

    for i in range(len(df)):
        # 原始数据波动率 (high/low - 1)
        if df['low'].iloc[i] > 0:
            orig_vol = df['high'].iloc[i] / df['low'].iloc[i] - 1
            original_volatilities.append(orig_vol)

        # 平滑后数据波动率 (high/low - 1)
        if df_smoothed['low'].iloc[i] > 0:
            smooth_vol = df_smoothed['high'].iloc[i] / df_smoothed['low'].iloc[i] - 1
            smoothed_volatilities.append(smooth_vol)

    avg_original_vol = np.mean(original_volatilities) if original_volatilities else 0
    avg_smoothed_vol = np.mean(smoothed_volatilities) if smoothed_volatilities else 0
    max_original_vol = max(original_volatilities) if original_volatilities else 0
    max_smoothed_vol = max(smoothed_volatilities) if smoothed_volatilities else 0

    # 统计超过限制的K线数量
    over_limit_original = sum(1 for vol in original_volatilities if vol > max_volatility)
    over_limit_smoothed = sum(1 for vol in smoothed_volatilities if vol > max_volatility)

    print(f"OHLC平滑优化完成 (包含长影线缩短):")
    print(f"  优化操作次数: {optimization_count}")
    print(f"  平均波动率: {avg_original_vol:.4f} → {avg_smoothed_vol:.4f}")
    print(f"  最大波动率: {max_original_vol:.4f} → {max_smoothed_vol:.4f}")
    print(f"  超过{max_volatility*100:.1f}%限制的K线: {over_limit_original} → {over_limit_smoothed}")

    if over_limit_smoothed == 0:
        print(f"✓ 所有K线波动已控制在{max_volatility*100:.1f}%以内，长影线已缩短")
    else:
        print(f"⚠ 仍有{over_limit_smoothed}根K线超过限制，可能需要进一步调整参数")

    return df_smoothed

if __name__ == "__main__":
    # 执行K线数据生成
    kline_data = process_bybusdt_trades_to_kline()